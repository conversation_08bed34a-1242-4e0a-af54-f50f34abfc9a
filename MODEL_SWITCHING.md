# Model Switching Feature

## Overview
The Neurino application now supports dynamic switching between different Gemini AI models, allowing users to choose the best model for their specific needs.

## Available Models

### 🟣 Gemini 2.5 Pro
- **Best for**: Complex reasoning, detailed analysis, comprehensive responses
- **Performance**: Highest capability, slower response times
- **Use cases**: Research, detailed explanations, complex problem-solving

### 🔵 Gemini 2.5 Flash (Default)
- **Best for**: Balanced performance and speed
- **Performance**: Good capability with reasonable response times
- **Use cases**: General questions, everyday tasks, balanced workflows

### 🟢 Gemini 2.5 Flash Lite
- **Best for**: Quick responses, simple questions
- **Performance**: Fastest response times, lightweight processing
- **Use cases**: Quick facts, simple queries, rapid iterations

## How to Use

1. **Set up API Key**: First, add your Gemini API key in the toolbar
2. **Select Model**: Use the model selector dropdown in the toolbar to choose your preferred model
3. **Ask Questions**: The selected model will be used for all new AI queries
4. **Switch Anytime**: Change models at any time - your selection is automatically saved

## Features

- ✅ **Persistent Selection**: Your model choice is saved and restored when you reload the page
- ✅ **Visual Feedback**: The model selector shows which model is currently selected
- ✅ **Smart Disabling**: Model selector is disabled when no API key is configured
- ✅ **Generation Indicator**: Visual feedback when AI is generating responses
- ✅ **Error Handling**: Helpful error messages for model-specific issues
- ✅ **Seamless Integration**: Works with all existing features (search grounding, parent context, etc.)

## Technical Implementation

### Components Added
- `ModelSelector.tsx`: Dropdown component for model selection
- `models.ts`: Configuration file with model definitions

### Components Modified
- `Toolbar.tsx`: Added model selector integration
- `Node.tsx`: Updated to pass selected model to API calls
- `MindMap.tsx`: Updated to manage model state
- `useMindMap.ts`: Added model persistence logic
- `gemini.ts`: Updated to accept and use selected model

### Storage
- Model selection is persisted in localStorage under the key `gemini-selected-model`
- Defaults to Gemini 2.5 Flash if no selection is saved

## Error Handling

The application includes enhanced error handling for:
- Invalid or unsupported models
- API key access issues for specific models
- Safety filter blocks
- Network connectivity issues
- Rate limiting and quota exceeded scenarios

## Future Enhancements

Potential improvements for future versions:
- Model-specific configuration options (temperature, max tokens)
- Usage analytics and cost tracking
- Model recommendation based on query type
- Batch model switching for multiple nodes
