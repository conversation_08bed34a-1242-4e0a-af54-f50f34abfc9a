import { GeminiModel } from '../types';

export const GEMINI_MODELS: GeminiModel[] = [
  {
    id: 'gemini-1.5-pro',
    name: 'Gemini 1.5 Pro',
    description: 'High capability with search grounding support',
    maxTokens: 8192,
    supportsSearchGrounding: true,
  },
  {
    id: 'gemini-1.5-flash',
    name: 'Gemini 1.5 Flash',
    description: 'Balanced performance with search grounding support',
    maxTokens: 8192,
    supportsSearchGrounding: true,
  },
  {
    id: 'gemini-2.5-pro',
    name: 'Gemini 2.5 Pro',
    description: 'Highest capability with search grounding support',
    maxTokens: 8192,
    supportsSearchGrounding: true,
  },
  {
    id: 'gemini-2.5-flash',
    name: 'Gemini 2.5 Flash',
    description: 'Balanced performance and speed with search grounding',
    maxTokens: 8192,
    isDefault: true,
    supportsSearchGrounding: true,
  },
  {
    id: 'gemini-2.5-flash-lite-preview-06-17',
    name: 'Gemini 2.5 Flash Lite',
    description: 'Fastest responses with search grounding support',
    maxTokens: 8192,
    supportsSearchGrounding: true,
  },
];

export const DEFAULT_MODEL = GEMINI_MODELS.find(model => model.isDefault) || GEMINI_MODELS[1];

export const getModelById = (id: string): GeminiModel | undefined => {
  return GEMINI_MODELS.find(model => model.id === id);
};

export const getModelDisplayName = (id: string): string => {
  const model = getModelById(id);
  return model ? model.name : id;
};

export const getModelDescription = (id: string): string => {
  const model = getModelById(id);
  return model ? model.description : 'Unknown model';
};

export const modelSupportsSearchGrounding = (id: string): boolean => {
  const model = getModelById(id);
  return model?.supportsSearchGrounding ?? false;
};
