/**
 * Utility functions for clipboard operations
 */

export interface CopyResult {
  success: boolean;
  error?: string;
}

/**
 * Copies text to clipboard with fallback for older browsers
 * @param text - The text to copy to clipboard
 * @returns Promise<CopyResult> - Result of the copy operation
 */
export const copyToClipboard = async (text: string): Promise<CopyResult> => {
  // Check if the Clipboard API is available (modern browsers)
  if (navigator.clipboard && window.isSecureContext) {
    try {
      await navigator.clipboard.writeText(text);
      return { success: true };
    } catch (error) {
      console.warn('Clipboard API failed, falling back to legacy method:', error);
      // Fall through to legacy method
    }
  }

  // Fallback for older browsers or when Clipboard API fails
  try {
    return await fallbackCopyToClipboard(text);
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to copy to clipboard'
    };
  }
};

/**
 * Legacy clipboard copy method using document.execCommand
 * @param text - The text to copy
 * @returns Promise<CopyResult> - Result of the copy operation
 */
const fallbackCopyToClipboard = async (text: string): Promise<CopyResult> => {
  return new Promise((resolve) => {
    // Create a temporary textarea element
    const textArea = document.createElement('textarea');
    textArea.value = text;
    
    // Make the textarea invisible but still focusable
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    textArea.style.opacity = '0';
    textArea.setAttribute('readonly', '');
    textArea.setAttribute('aria-hidden', 'true');
    
    document.body.appendChild(textArea);
    
    try {
      // Select the text
      textArea.focus();
      textArea.select();
      textArea.setSelectionRange(0, text.length);
      
      // Try to copy using execCommand
      const successful = document.execCommand('copy');
      
      if (successful) {
        resolve({ success: true });
      } else {
        resolve({
          success: false,
          error: 'Copy command was unsuccessful'
        });
      }
    } catch (error) {
      resolve({
        success: false,
        error: error instanceof Error ? error.message : 'Copy operation failed'
      });
    } finally {
      // Clean up the temporary element
      document.body.removeChild(textArea);
    }
  });
};

/**
 * Strips markdown formatting from text for plain text copying
 * @param markdownText - Text with markdown formatting
 * @returns Plain text without markdown formatting
 */
export const stripMarkdown = (markdownText: string): string => {
  return markdownText
    // Remove headers
    .replace(/^#{1,6}\s+/gm, '')
    // Remove bold and italic
    .replace(/\*\*([^*]+)\*\*/g, '$1')
    .replace(/\*([^*]+)\*/g, '$1')
    .replace(/__([^_]+)__/g, '$1')
    .replace(/_([^_]+)_/g, '$1')
    // Remove links but keep text
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
    // Remove inline code
    .replace(/`([^`]+)`/g, '$1')
    // Remove code blocks
    .replace(/```[\s\S]*?```/g, '')
    // Remove horizontal rules
    .replace(/^---+$/gm, '')
    // Remove list markers
    .replace(/^[\s]*[-*+]\s+/gm, '')
    .replace(/^[\s]*\d+\.\s+/gm, '')
    // Clean up extra whitespace
    .replace(/\n\s*\n/g, '\n\n')
    .trim();
};

/**
 * Formats text for clipboard copying, optionally preserving or stripping markdown
 * @param text - The text to format
 * @param preserveMarkdown - Whether to preserve markdown formatting (default: false)
 * @returns Formatted text ready for clipboard
 */
export const formatTextForClipboard = (text: string, preserveMarkdown: boolean = false): string => {
  if (preserveMarkdown) {
    return text.trim();
  }
  return stripMarkdown(text);
};
