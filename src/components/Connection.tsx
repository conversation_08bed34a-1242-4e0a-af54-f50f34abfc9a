import React from 'react';
import { NodeData } from '../types';

interface ConnectionProps {
  parent: NodeData;
  child: NodeData;
  viewport: { x: number; y: number; zoom: number };
}

export const Connection: React.FC<ConnectionProps> = ({ parent, child, viewport }) => {
  // Only hide connections if the parent is collapsed AND has children
  // This ensures we show the connection line to collapsed nodes
  if (!parent.isExpanded && parent.childIds.length > 0) {
    // Still show connection to immediate children, but not to grandchildren
    const isDirectChild = parent.childIds.includes(child.id);
    if (!isDirectChild) return null;
  }

  const startX = parent.x + parent.width;
  const startY = parent.y + 30;
  const endX = child.x;
  const endY = child.y + 30;

  const midX = startX + (endX - startX) / 2;

  const pathData = `M ${startX} ${startY} C ${midX} ${startY}, ${midX} ${endY}, ${endX} ${endY}`;

  return (
    <svg
      className="absolute inset-0 pointer-events-none overflow-visible"
      style={{
        left: 0,
        top: 0,
        width: '100%',
        height: '100%',
        zIndex: 1,
      }}
    >
      <defs>
        <marker
          id={`arrowhead-${parent.id}-${child.id}`}
          markerWidth="10"
          markerHeight="7"
          refX="9"
          refY="3.5"
          orient="auto"
          markerUnits="strokeWidth"
        >
          <polygon
            points="0 0, 10 3.5, 0 7"
            fill="#6b7280"
            className="dark:fill-gray-400"
          />
        </marker>
      </defs>
      <path
        d={pathData}
        stroke="#6b7280"
        strokeWidth="2"
        fill="none"
        markerEnd={`url(#arrowhead-${parent.id}-${child.id})`}
        className="opacity-70 dark:stroke-gray-400"
      />
    </svg>
  );
};