import React from 'react';
import { ExternalLink, Twitter } from 'lucide-react';

export const SocialLinks: React.FC = () => {
  return (
    <div className="fixed top-4 right-4 bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-lg border border-gray-200/50 dark:border-gray-700/50 p-2 flex items-center gap-2 z-50 shadow-lg">
      <a
        href="https://bagusfarisa.com"
        target="_blank"
        rel="noopener noreferrer"
        className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-md transition-colors text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
        title="Personal Website"
      >
        <ExternalLink size={18} />
      </a>
      <a
        href="https://twitter.com/bagusfarisa"
        target="_blank"
        rel="noopener noreferrer"
        className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-md transition-colors text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
        title="Twitter"
      >
        <Twitter size={18} />
      </a>
    </div>
  );
};
