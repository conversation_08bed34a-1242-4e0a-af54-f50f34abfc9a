export interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: number;
  sources?: string[];
}

export interface GeminiModel {
  id: string;
  name: string;
  description: string;
  maxTokens: number;
  isDefault?: boolean;
  supportsSearchGrounding?: boolean;
}

export interface ModelConfig {
  selectedModel: string;
  availableModels: GeminiModel[];
}

export interface NodeData {
  id: string;
  x: number;
  y: number;
  title: string;
  query?: string; // Single user query
  response?: string; // Single AI response
  sources?: Array<{title: string; uri: string}>; // Sources with title and URI from grounding
  messages: ChatMessage[]; // Keep for backward compatibility
  isExpanded: boolean;
  parentId?: string;
  childIds: string[];
  isSelected: boolean;
  width: number;
  height: number;
  searchGrounding: boolean;
  hasQueried: boolean; // Track if user has already asked a question
}

export interface MindMapData {
  nodes: Record<string, NodeData>;
  rootNodeId: string;
  searchGrounding: boolean; // Keep global default for new nodes
  selectedModel?: string; // Selected Gemini model
  version: number;
  lastModified: number;
}

export interface ViewportState {
  x: number;
  y: number;
  zoom: number;
}