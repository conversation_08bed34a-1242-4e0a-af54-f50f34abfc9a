# Search Grounding Fix Test Plan

## Issue Fixed
The search grounding functionality was broken after implementing the model switcher because Gemini 2.5 models use a different API syntax (`googleSearch`) compared to older models (`googleSearchRetrieval`).

## Solution Implemented
1. **Updated API syntax handling** - Modified gemini.ts to use the correct tool syntax based on model version
2. **Added model compatibility flags** - Added `supportsSearchGrounding` property to model definitions
3. **Updated model configuration** - All Gemini models now support search grounding with proper API syntax
4. **Enhanced UI feedback** - Modified Node and ModelSelector components to show search grounding support status

## Test Steps

### 1. Test Model Selector
- [ ] Open the application
- [ ] Click on the model selector dropdown
- [ ] Verify that ALL models show a search icon (🔍) indicating search grounding support
- [ ] Select any model (default is Gemini 2.5 Flash)

### 2. Test Search Grounding with Gemini 1.5 Models
- [ ] Select a Gemini 1.5 model (e.g., Gemini 1.5 Flash)
- [ ] Create a new node
- [ ] Click the search grounding toggle button (🔍) - it should be enabled
- [ ] Verify the button turns blue when enabled
- [ ] Enter a query that would benefit from search grounding (e.g., "What's the weather in New York today?")
- [ ] Submit the query
- [ ] Verify that the response includes sources/citations if search grounding worked

### 3. Test Search Grounding with Gemini 2.5 Models
- [ ] Select a Gemini 2.5 model (e.g., Gemini 2.5 Flash - the default)
- [ ] Create a new node or use existing node
- [ ] Click the search grounding toggle button (🔍) - it should be enabled
- [ ] Verify the button turns blue when enabled
- [ ] Enter a query that would benefit from search grounding (e.g., "Who won Euro 2024?")
- [ ] Submit the query
- [ ] Verify that the response includes sources/citations if search grounding worked

### 4. Test Model Switching Behavior
- [ ] Enable search grounding on a node with any model
- [ ] Switch between different models (1.5 and 2.5)
- [ ] Verify the search grounding toggle remains functional for all models
- [ ] Test queries with different models to ensure search grounding works consistently

## Expected Results
- ✅ Search grounding works with ALL Gemini models (1.5 and 2.5)
- ✅ Gemini 1.5 models use `googleSearchRetrieval` API syntax
- ✅ Gemini 2.5 models use `googleSearch` API syntax
- ✅ UI clearly indicates that all models support search grounding
- ✅ No errors when switching between models
- ✅ Consistent search grounding functionality across all model versions
